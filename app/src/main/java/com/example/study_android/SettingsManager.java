package com.example.study_android;

import android.content.Context;
import android.content.SharedPreferences;

public class SettingsManager {
    private static final String PREF_NAME = "browser_settings";
    private static final String KEY_CACHE_ENABLED = "cache_enabled";
    private static final String KEY_OFFLINE_MODE = "offline_mode";
    private static final String KEY_FULLSCREEN_MODE = "fullscreen_mode";
    
    private SharedPreferences sharedPreferences;
    
    public SettingsManager(Context context) {
        sharedPreferences = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
    }
    
    public boolean isCacheEnabled() {
        return sharedPreferences.getBoolean(KEY_CACHE_ENABLED, true);
    }
    
    public void setCacheEnabled(boolean enabled) {
        SharedPreferences.Editor editor = sharedPreferences.edit();
        editor.putBoolean(KEY_CACHE_ENABLED, enabled);
        editor.apply();
    }
    
    public boolean isOfflineMode() {
        return sharedPreferences.getBoolean(KEY_OFFLINE_MODE, false);
    }
    
    public void setOfflineMode(boolean enabled) {
        SharedPreferences.Editor editor = sharedPreferences.edit();
        editor.putBoolean(KEY_OFFLINE_MODE, enabled);
        editor.apply();
    }
    
    public boolean isFullscreenMode() {
        return sharedPreferences.getBoolean(KEY_FULLSCREEN_MODE, false);
    }
    
    public void setFullscreenMode(boolean enabled) {
        SharedPreferences.Editor editor = sharedPreferences.edit();
        editor.putBoolean(KEY_FULLSCREEN_MODE, enabled);
        editor.apply();
    }
}