package com.example.study_android;

import android.app.AlertDialog;
import android.content.Intent;
import android.os.Bundle;
import android.os.Environment;
import android.widget.Button;
import android.widget.ListView;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.List;

public class HistoryActivity extends AppCompatActivity implements HistoryAdapter.OnHistoryItemClickListener {
    private ListView listHistory;
    private Button btnBack, btnSelectAll, btnDeleteSelected, btnClearAll, btnExport, btnImport;
    private HistoryManager historyManager;
    private HistoryAdapter adapter;
    private boolean isAllSelected = false;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_history);

        initViews();
        initData();
        setupListeners();
    }

    private void initViews() {
        listHistory = findViewById(R.id.listHistory);
        btnBack = findViewById(R.id.btnBack);
        btnSelectAll = findViewById(R.id.btnSelectAll);
        btnDeleteSelected = findViewById(R.id.btnDeleteSelected);
        btnClearAll = findViewById(R.id.btnClearAll);
        btnExport = findViewById(R.id.btnExport);
        btnImport = findViewById(R.id.btnImport);
    }

    private void initData() {
        historyManager = new HistoryManager(this);
        List<HistoryItem> historyList = historyManager.getHistory();
        adapter = new HistoryAdapter(this, historyList);
        adapter.setOnHistoryItemClickListener(this);
        listHistory.setAdapter(adapter);
    }

    private void setupListeners() {
        btnBack.setOnClickListener(v -> finish());

        btnSelectAll.setOnClickListener(v -> {
            isAllSelected = !isAllSelected;
            historyManager.selectAll(isAllSelected);
            adapter.notifyDataSetChanged();
            btnSelectAll.setText(isAllSelected ? "取消全选" : "全选");
        });

        btnDeleteSelected.setOnClickListener(v -> {
            new AlertDialog.Builder(this)
                    .setTitle("确认删除")
                    .setMessage("确定要删除选中的历史记录吗？")
                    .setPositiveButton("确定", (dialog, which) -> {
                        historyManager.deleteSelectedHistory();
                        refreshList();
                        Toast.makeText(this, "删除成功", Toast.LENGTH_SHORT).show();
                    })
                    .setNegativeButton("取消", null)
                    .show();
        });

        btnClearAll.setOnClickListener(v -> {
            new AlertDialog.Builder(this)
                    .setTitle("确认清空")
                    .setMessage("确定要清空所有历史记录吗？此操作不可恢复！")
                    .setPositiveButton("确定", (dialog, which) -> {
                        historyManager.clearAllHistory();
                        refreshList();
                        Toast.makeText(this, "清空成功", Toast.LENGTH_SHORT).show();
                    })
                    .setNegativeButton("取消", null)
                    .show();
        });

        btnExport.setOnClickListener(v -> exportHistory());
        btnImport.setOnClickListener(v -> importHistory());
    }

    private void refreshList() {
        List<HistoryItem> historyList = historyManager.getHistory();
        adapter.updateData(historyList);
        isAllSelected = false;
        btnSelectAll.setText("全选");
    }

    private void exportHistory() {
        try {
            String jsonData = historyManager.exportToJson();
            File downloadsDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS);
            File exportFile = new File(downloadsDir, "browser_history_" + System.currentTimeMillis() + ".json");
            
            FileOutputStream fos = new FileOutputStream(exportFile);
            fos.write(jsonData.getBytes());
            fos.close();
            
            Toast.makeText(this, "导出成功：" + exportFile.getAbsolutePath(), Toast.LENGTH_LONG).show();
        } catch (IOException e) {
            e.printStackTrace();
            Toast.makeText(this, "导出失败：" + e.getMessage(), Toast.LENGTH_SHORT).show();
        }
    }

    private void importHistory() {
        // 简化版本：从Downloads目录选择最新的JSON文件
        try {
            File downloadsDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS);
            File[] files = downloadsDir.listFiles((dir, name) -> name.endsWith(".json") && name.contains("browser_history"));
            
            if (files == null || files.length == 0) {
                Toast.makeText(this, "在Downloads目录中未找到历史记录文件", Toast.LENGTH_SHORT).show();
                return;
            }
            
            // 选择最新的文件
            File latestFile = files[0];
            for (File file : files) {
                if (file.lastModified() > latestFile.lastModified()) {
                    latestFile = file;
                }
            }
            
            FileInputStream fis = new FileInputStream(latestFile);
            byte[] data = new byte[(int) latestFile.length()];
            fis.read(data);
            fis.close();
            
            String jsonData = new String(data);
            boolean success = historyManager.importFromJson(jsonData);
            
            if (success) {
                refreshList();
                Toast.makeText(this, "导入成功", Toast.LENGTH_SHORT).show();
            } else {
                Toast.makeText(this, "导入失败：文件格式错误", Toast.LENGTH_SHORT).show();
            }
        } catch (IOException e) {
            e.printStackTrace();
            Toast.makeText(this, "导入失败：" + e.getMessage(), Toast.LENGTH_SHORT).show();
        }
    }

    @Override
    public void onItemClick(HistoryItem item) {
        // 返回选中的URL到主界面
        Intent intent = new Intent();
        intent.putExtra("selected_url", item.getUrl());
        setResult(RESULT_OK, intent);
        finish();
    }

    @Override
    public void onDeleteClick(HistoryItem item) {
        new AlertDialog.Builder(this)
                .setTitle("确认删除")
                .setMessage("确定要删除这条历史记录吗？")
                .setPositiveButton("确定", (dialog, which) -> {
                    historyManager.deleteHistory(item);
                    refreshList();
                    Toast.makeText(this, "删除成功", Toast.LENGTH_SHORT).show();
                })
                .setNegativeButton("取消", null)
                .show();
    }

    @Override
    public void onCheckChanged(HistoryItem item, boolean isChecked) {
        // CheckBox状态已在adapter中处理
    }
}