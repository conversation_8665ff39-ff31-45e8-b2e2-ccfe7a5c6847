package com.example.study_android;

import android.webkit.WebChromeClient;
import android.webkit.WebView;

public class CustomWebChromeClient extends WebChromeClient {
    private MainActivity mainActivity;
    
    public CustomWebChromeClient(MainActivity activity) {
        this.mainActivity = activity;
    }
    
    @Override
    public void onReceivedTitle(WebView view, String title) {
        super.onReceivedTitle(view, title);
        String currentTitle = title != null ? title : "";
        mainActivity.setCurrentTitle(currentTitle);
    }

    @Override
    public void onProgressChanged(WebView view, int newProgress) {
        super.onProgressChanged(view, newProgress);
        // 可以在这里添加进度条显示
    }
}
