package com.example.study_android;

import android.graphics.Bitmap;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.EditText;
import android.widget.Button;
import android.widget.Toast;

public class CustomWebViewClient extends WebViewClient {
    private MainActivity mainActivity;
    private EditText etUrl;
    private Button btnBack;
    private Button btnForward;
    private HistoryManager historyManager;
    
    public CustomWebViewClient(MainActivity activity, EditText etUrl, Button btnBack, Button btnForward, HistoryManager historyManager) {
        this.mainActivity = activity;
        this.etUrl = etUrl;
        this.btnBack = btnBack;
        this.btnForward = btnForward;
        this.historyManager = historyManager;
    }
    
    @Override
    public void onPageStarted(WebView view, String url, Bitmap favicon) {
        super.onPageStarted(view, url, favicon);
        String currentUrl = url != null ? url : "";
        mainActivity.setCurrentUrl(currentUrl);
        etUrl.setText(currentUrl);
    }

    @Override
    public void onPageFinished(WebView view, String url) {
        super.onPageFinished(view, url);
        String currentUrl = url != null ? url : "";
        mainActivity.setCurrentUrl(currentUrl);
        etUrl.setText(currentUrl);
        
        // 添加到历史记录
        String currentTitle = mainActivity.getCurrentTitle();
        if (currentTitle == null || currentTitle.isEmpty()) {
            String title = view.getTitle();
            currentTitle = title != null ? title : "";
            mainActivity.setCurrentTitle(currentTitle);
        }
        historyManager.addHistory(currentTitle, currentUrl);
        
        // 更新按钮状态
        btnBack.setEnabled(view.canGoBack());
        btnForward.setEnabled(view.canGoForward());
    }

    @Override
    public void onReceivedError(WebView view, int errorCode, String description, String failingUrl) {
        super.onReceivedError(view, errorCode, description, failingUrl);
        Toast.makeText(mainActivity, "加载失败: " + description, Toast.LENGTH_SHORT).show();
    }
}
