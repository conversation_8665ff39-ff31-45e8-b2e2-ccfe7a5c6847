<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- 标题栏 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:paddingBottom="24dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="设置"
                android:textSize="18sp"
                android:textStyle="bold" />

            <Button
                android:id="@+id/btnBack"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="返回" />

        </LinearLayout>

        <!-- 缓存设置 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="缓存设置"
            android:textSize="16sp"
            android:textStyle="bold"
            android:paddingBottom="8dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:paddingBottom="8dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="启用网页缓存" />

            <Switch
                android:id="@+id/switchCache"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:checked="true" />

        </LinearLayout>

        <Button
            android:id="@+id/btnClearCache"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="清理缓存"
            android:layout_marginBottom="24dp" />

        <!-- 网络设置 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="网络设置"
            android:textSize="16sp"
            android:textStyle="bold"
            android:paddingBottom="8dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:paddingBottom="8dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="离线模式（模拟断网）" />

            <Switch
                android:id="@+id/switchOffline"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:checked="false" />

        </LinearLayout>

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="离线模式下将优先加载缓存内容"
            android:textSize="12sp"
            android:textColor="#666666"
            android:layout_marginBottom="24dp" />

        <!-- 显示设置 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="显示设置"
            android:textSize="16sp"
            android:textStyle="bold"
            android:paddingBottom="8dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:paddingBottom="8dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="全屏显示" />

            <Switch
                android:id="@+id/switchFullscreen"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:checked="false" />

        </LinearLayout>

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="全屏模式下将隐藏工具栏和功能栏"
            android:textSize="12sp"
            android:textColor="#666666"
            android:layout_marginBottom="24dp" />

        <!-- 其他设置 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="其他设置"
            android:textSize="16sp"
            android:textStyle="bold"
            android:paddingBottom="8dp" />

        <Button
            android:id="@+id/btnAbout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="关于应用" />

    </LinearLayout>

</ScrollView>