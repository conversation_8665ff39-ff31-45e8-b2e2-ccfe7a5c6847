<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp">

    <!-- 标题栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:paddingBottom="16dp">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="浏览历史"
            android:textSize="18sp"
            android:textStyle="bold" />

        <Button
            android:id="@+id/btnBack"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="返回" />

    </LinearLayout>

    <!-- 操作按钮栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingBottom="16dp">

        <Button
            android:id="@+id/btnSelectAll"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="全选"
            android:layout_marginRight="4dp" />

        <Button
            android:id="@+id/btnDeleteSelected"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="删除选中"
            android:layout_marginLeft="4dp"
            android:layout_marginRight="4dp" />

        <Button
            android:id="@+id/btnClearAll"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="清空全部"
            android:layout_marginLeft="4dp" />

    </LinearLayout>

    <!-- 导入导出按钮栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingBottom="16dp">

        <Button
            android:id="@+id/btnExport"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="导出JSON"
            android:layout_marginRight="8dp" />

        <Button
            android:id="@+id/btnImport"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="导入JSON"
            android:layout_marginLeft="8dp" />

    </LinearLayout>

    <!-- 历史记录列表 -->
    <ListView
        android:id="@+id/listHistory"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:choiceMode="multipleChoice" />

</LinearLayout>